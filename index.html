<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لعبة الثعبان</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🐍 لعبة الثعبان</h1>
            <div class="score-board">
                <div class="score">النقاط: <span id="score">0</span></div>
                <div class="high-score">أفضل نتيجة: <span id="highScore">0</span></div>
            </div>
        </header>

        <div class="game-container">
            <canvas id="gameCanvas" width="400" height="400"></canvas>
            
            <div class="game-overlay" id="gameOverlay">
                <div class="overlay-content">
                    <h2 id="overlayTitle">اضغط مسافة للبدء</h2>
                    <p id="overlayMessage">استخدم الأسهم للتحكم في الثعبان</p>
                    <button id="startButton" class="game-button">ابدأ اللعبة</button>
                </div>
            </div>
        </div>

        <div class="controls">
            <h3>التحكم:</h3>
            <div class="control-grid">
                <div></div>
                <button class="control-btn" data-direction="up">↑</button>
                <div></div>
                <button class="control-btn" data-direction="left">←</button>
                <button class="control-btn" data-direction="down">↓</button>
                <button class="control-btn" data-direction="right">→</button>
            </div>
            <p>أو استخدم أسهم الكيبورد</p>
        </div>

        <div class="instructions">
            <h3>كيفية اللعب:</h3>
            <ul>
                <li>تحكم في الثعبان باستخدام الأسهم</li>
                <li>اجمع الطعام الأحمر لتكبر وتحصل على نقاط</li>
                <li>تجنب الاصطدام بالجدران أو بجسم الثعبان</li>
                <li>اضغط مسافة لإيقاف/استكمال اللعبة</li>
            </ul>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
