// Game variables
const canvas = document.getElementById('gameCanvas');
const ctx = canvas.getContext('2d');
const scoreElement = document.getElementById('score');
const highScoreElement = document.getElementById('highScore');
const gameOverlay = document.getElementById('gameOverlay');
const overlayTitle = document.getElementById('overlayTitle');
const overlayMessage = document.getElementById('overlayMessage');
const startButton = document.getElementById('startButton');
const soundToggle = document.getElementById('soundToggle');

// Game settings
const GRID_SIZE = 20;
const CANVAS_SIZE = 400;
const GRID_COUNT = CANVAS_SIZE / GRID_SIZE;

// Game state
let gameState = 'menu'; // 'menu', 'playing', 'paused', 'gameOver', 'modeSelect'
let gameMode = 'classic'; // 'classic', 'challenge', 'infinite'
let score = 0;
let highScore = localStorage.getItem('snakeHighScore') || 0;
let gameLoop;
let animationFrame;
let gameSpeed = 150;
let level = 1;
let combo = 0;
let achievements = JSON.parse(localStorage.getItem('snakeAchievements') || '[]');
let totalGamesPlayed = parseInt(localStorage.getItem('totalGamesPlayed') || '0');

// Game mode settings
let gameModes = {
    classic: { name: 'كلاسيكي', description: 'اللعبة التقليدية', obstacles: false, portals: false },
    challenge: { name: 'تحدي', description: 'مع عوائق متحركة', obstacles: true, portals: true },
    infinite: { name: 'لا نهائي', description: 'بدون جدران', obstacles: false, portals: true, wallWrap: true }
};

// Snake with smooth movement
let snake = [
    { x: 10, y: 10, smoothX: 10 * GRID_SIZE, smoothY: 10 * GRID_SIZE }
];
let direction = { x: 0, y: 0 };
let nextDirection = { x: 0, y: 0 };

// Enhanced Food System
let foods = [];
let foodTypes = {
    normal: { color: '#FF4444', points: 10, effect: null, glow: '#FF6666', emoji: '🍎' },
    golden: { color: '#FFD700', points: 50, effect: 'speed', glow: '#FFFF44', emoji: '🥇' },
    magic: { color: '#9C27B0', points: 25, effect: 'double', glow: '#E1BEE7', emoji: '💜' },
    mega: { color: '#00BCD4', points: 100, effect: 'mega', glow: '#4DD0E1', emoji: '💎' },
    rainbow: { color: '#FF69B4', points: 200, effect: 'rainbow', glow: '#FFB6C1', emoji: '🌈' },
    bomb: { color: '#FF0000', points: -50, effect: 'bomb', glow: '#FF4444', emoji: '💣' }
};

// Game Objects
let obstacles = [];
let portals = [];
let shockwaves = [];
let raindrops = [];

// Particle System
let particles = [];
let backgroundParticles = [];
let neonRain = [];

// Power-ups and Effects
let activePowerUps = [];
let powerUpDuration = 5000; // 5 seconds

// Animation and Visual Effects
let time = 0;
let explosions = [];
let screenShake = { x: 0, y: 0, intensity: 0 };

// Achievement System
let achievementList = [
    { id: 'first_game', name: 'البداية', description: 'العب أول لعبة', unlocked: false },
    { id: 'score_100', name: 'المبتدئ', description: 'احصل على 100 نقطة', unlocked: false },
    { id: 'score_500', name: 'المحترف', description: 'احصل على 500 نقطة', unlocked: false },
    { id: 'combo_10', name: 'كومبو ماستر', description: 'احصل على كومبو ×10', unlocked: false },
    { id: 'rainbow_eater', name: 'صياد قوس القزح', description: 'كل 5 أطعمة قوس قزح', unlocked: false },
    { id: 'speed_demon', name: 'شيطان السرعة', description: 'اوصل للمستوى 10', unlocked: false }
];

// Audio System
let audioContext;
let soundEnabled = true;

// Initialize audio
function initAudio() {
    try {
        audioContext = new (window.AudioContext || window.webkitAudioContext)();
    } catch (e) {
        console.log('Web Audio API not supported');
        soundEnabled = false;
    }
}

// Play sound effect
function playSound(frequency, duration, type = 'sine') {
    if (!soundEnabled || !audioContext) return;

    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
    oscillator.type = type;

    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + duration);
}

// Particle Class
class Particle {
    constructor(x, y, color, size = 3, velocity = null) {
        this.x = x;
        this.y = y;
        this.color = color;
        this.size = size;
        this.life = 1.0;
        this.decay = Math.random() * 0.02 + 0.01;
        this.velocity = velocity || {
            x: (Math.random() - 0.5) * 4,
            y: (Math.random() - 0.5) * 4
        };
    }

    update() {
        this.x += this.velocity.x;
        this.y += this.velocity.y;
        this.life -= this.decay;
        this.velocity.x *= 0.98;
        this.velocity.y *= 0.98;
    }

    draw() {
        ctx.save();
        ctx.globalAlpha = this.life;
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.size * this.life, 0, Math.PI * 2);
        ctx.fill();
        ctx.restore();
    }
}

// Explosion Effect
class Explosion {
    constructor(x, y, color) {
        this.x = x;
        this.y = y;
        this.particles = [];
        for (let i = 0; i < 15; i++) {
            this.particles.push(new Particle(x, y, color, Math.random() * 5 + 2));
        }
        this.life = 1.0;
    }

    update() {
        this.particles.forEach(particle => particle.update());
        this.particles = this.particles.filter(particle => particle.life > 0);
        this.life -= 0.05;
    }

    draw() {
        this.particles.forEach(particle => particle.draw());
    }
}

// Obstacle Class
class Obstacle {
    constructor(x, y, type = 'static') {
        this.x = x;
        this.y = y;
        this.type = type;
        this.pulse = 0;
        this.moveDirection = Math.random() < 0.5 ? 1 : -1;
        this.moveSpeed = 0.02;
    }

    update() {
        this.pulse += 0.1;
        if (this.type === 'moving') {
            this.x += this.moveDirection * this.moveSpeed;
            if (this.x <= 0 || this.x >= GRID_COUNT - 1) {
                this.moveDirection *= -1;
            }
        }
    }

    draw() {
        const x = this.x * GRID_SIZE;
        const y = this.y * GRID_SIZE;
        const pulseSize = Math.sin(this.pulse) * 2 + GRID_SIZE;

        ctx.save();
        ctx.shadowColor = '#FF0000';
        ctx.shadowBlur = 15;
        ctx.fillStyle = '#AA0000';
        ctx.fillRect(x, y, GRID_SIZE, GRID_SIZE);

        ctx.shadowBlur = 5;
        ctx.fillStyle = '#FF4444';
        ctx.fillRect(x + 2, y + 2, GRID_SIZE - 4, GRID_SIZE - 4);
        ctx.restore();
    }
}

// Portal Class
class Portal {
    constructor(x1, y1, x2, y2) {
        this.entrance = { x: x1, y: y1 };
        this.exit = { x: x2, y: y2 };
        this.pulse = 0;
        this.particles = [];
        this.cooldown = 0;
    }

    update() {
        this.pulse += 0.15;
        this.cooldown = Math.max(0, this.cooldown - 1);

        // Add swirling particles
        if (Math.random() < 0.3) {
            this.particles.push(new Particle(
                this.entrance.x * GRID_SIZE + GRID_SIZE/2,
                this.entrance.y * GRID_SIZE + GRID_SIZE/2,
                '#00FFFF',
                2
            ));
            this.particles.push(new Particle(
                this.exit.x * GRID_SIZE + GRID_SIZE/2,
                this.exit.y * GRID_SIZE + GRID_SIZE/2,
                '#FF00FF',
                2
            ));
        }

        this.particles.forEach(particle => particle.update());
        this.particles = this.particles.filter(particle => particle.life > 0);
    }

    draw() {
        // Draw entrance portal
        this.drawPortalRing(this.entrance.x, this.entrance.y, '#00FFFF');
        // Draw exit portal
        this.drawPortalRing(this.exit.x, this.exit.y, '#FF00FF');

        // Draw particles
        this.particles.forEach(particle => particle.draw());
    }

    drawPortalRing(x, y, color) {
        const centerX = x * GRID_SIZE + GRID_SIZE/2;
        const centerY = y * GRID_SIZE + GRID_SIZE/2;
        const radius = Math.sin(this.pulse) * 3 + 8;

        ctx.save();
        ctx.strokeStyle = color;
        ctx.lineWidth = 3;
        ctx.shadowColor = color;
        ctx.shadowBlur = 10;
        ctx.beginPath();
        ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
        ctx.stroke();

        ctx.globalAlpha = 0.3;
        ctx.fillStyle = color;
        ctx.fill();
        ctx.restore();
    }
}

// Shockwave Effect
class Shockwave {
    constructor(x, y) {
        this.x = x;
        this.y = y;
        this.radius = 0;
        this.maxRadius = 100;
        this.life = 1.0;
    }

    update() {
        this.radius += 5;
        this.life -= 0.05;
    }

    draw() {
        if (this.life <= 0) return;

        ctx.save();
        ctx.globalAlpha = this.life;
        ctx.strokeStyle = '#00FF88';
        ctx.lineWidth = 3;
        ctx.shadowColor = '#00FF88';
        ctx.shadowBlur = 10;
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2);
        ctx.stroke();
        ctx.restore();
    }
}

// Initialize game
function init() {
    highScoreElement.textContent = highScore;
    updateOverlay();
    generateFood();
    initBackgroundParticles();
    initNeonRain();
    initAudio();
    loadAchievements();
    startAnimationLoop();

    // Event listeners
    startButton.addEventListener('click', showModeSelection);
    document.addEventListener('keydown', handleKeyPress);
    soundToggle.addEventListener('click', toggleSound);

    // Touch controls for mobile
    const controlBtns = document.querySelectorAll('.control-btn');
    controlBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            const dir = btn.dataset.direction;
            handleDirectionChange(dir);
        });
    });
}

// Initialize neon rain effect
function initNeonRain() {
    for (let i = 0; i < 30; i++) {
        neonRain.push({
            x: Math.random() * CANVAS_SIZE,
            y: Math.random() * CANVAS_SIZE,
            speed: Math.random() * 3 + 1,
            length: Math.random() * 20 + 10,
            color: `hsl(${Math.random() * 360}, 80%, 60%)`,
            opacity: Math.random() * 0.5 + 0.3
        });
    }
}

// Load achievements
function loadAchievements() {
    const saved = JSON.parse(localStorage.getItem('snakeAchievements') || '[]');
    achievementList.forEach(achievement => {
        if (saved.includes(achievement.id)) {
            achievement.unlocked = true;
        }
    });
}

// Background particles for ambiance
function initBackgroundParticles() {
    for (let i = 0; i < 50; i++) {
        backgroundParticles.push({
            x: Math.random() * CANVAS_SIZE,
            y: Math.random() * CANVAS_SIZE,
            size: Math.random() * 2 + 1,
            speed: Math.random() * 0.5 + 0.1,
            opacity: Math.random() * 0.5 + 0.1
        });
    }
}

// Show mode selection
function showModeSelection() {
    gameState = 'modeSelect';
    overlayTitle.textContent = 'اختر وضع اللعبة';
    overlayMessage.innerHTML = `
        <div class="mode-selection">
            <button class="mode-btn" onclick="selectMode('classic')">
                <h3>🎮 ${gameModes.classic.name}</h3>
                <p>${gameModes.classic.description}</p>
            </button>
            <button class="mode-btn" onclick="selectMode('challenge')">
                <h3>⚡ ${gameModes.challenge.name}</h3>
                <p>${gameModes.challenge.description}</p>
            </button>
            <button class="mode-btn" onclick="selectMode('infinite')">
                <h3>♾️ ${gameModes.infinite.name}</h3>
                <p>${gameModes.infinite.description}</p>
            </button>
        </div>
    `;
    startButton.textContent = 'العودة';
    startButton.onclick = () => {
        gameState = 'menu';
        updateOverlay();
        startButton.onclick = showModeSelection;
    };
    showOverlay();
}

// Select game mode
function selectMode(mode) {
    gameMode = mode;
    startGame();
}

function startGame() {
    gameState = 'playing';
    score = 0;
    level = 1;
    combo = 0;
    gameSpeed = 150;
    snake = [{ x: 10, y: 10, smoothX: 10 * GRID_SIZE, smoothY: 10 * GRID_SIZE }];
    direction = { x: 1, y: 0 };
    nextDirection = { x: 1, y: 0 };
    foods = [];
    particles = [];
    explosions = [];
    shockwaves = [];
    obstacles = [];
    portals = [];
    activePowerUps = [];
    screenShake = { x: 0, y: 0, intensity: 0 };

    // Initialize based on game mode
    initGameMode();
    generateFood();
    updateScore();
    hideOverlay();

    // Track games played
    totalGamesPlayed++;
    localStorage.setItem('totalGamesPlayed', totalGamesPlayed.toString());
    checkAchievement('first_game');

    if (gameLoop) clearInterval(gameLoop);
    gameLoop = setInterval(update, gameSpeed);
}

// Initialize game mode specific elements
function initGameMode() {
    if (gameModes[gameMode].obstacles) {
        // Add some obstacles for challenge mode
        for (let i = 0; i < 3; i++) {
            let obstacle;
            do {
                obstacle = new Obstacle(
                    Math.floor(Math.random() * GRID_COUNT),
                    Math.floor(Math.random() * GRID_COUNT),
                    Math.random() < 0.5 ? 'static' : 'moving'
                );
            } while (
                (obstacle.x === 10 && obstacle.y === 10) || // Not on snake start
                obstacles.some(obs => obs.x === obstacle.x && obs.y === obstacle.y)
            );
            obstacles.push(obstacle);
        }
    }

    if (gameModes[gameMode].portals) {
        // Add portals
        for (let i = 0; i < 2; i++) {
            let portal;
            do {
                portal = new Portal(
                    Math.floor(Math.random() * GRID_COUNT),
                    Math.floor(Math.random() * GRID_COUNT),
                    Math.floor(Math.random() * GRID_COUNT),
                    Math.floor(Math.random() * GRID_COUNT)
                );
            } while (
                (portal.entrance.x === 10 && portal.entrance.y === 10) ||
                (portal.exit.x === 10 && portal.exit.y === 10) ||
                obstacles.some(obs =>
                    (obs.x === portal.entrance.x && obs.y === portal.entrance.y) ||
                    (obs.x === portal.exit.x && obs.y === portal.exit.y)
                )
            );
            portals.push(portal);
        }
    }
}

// Achievement system
function checkAchievement(achievementId) {
    const achievement = achievementList.find(a => a.id === achievementId);
    if (achievement && !achievement.unlocked) {
        achievement.unlocked = true;
        showAchievementNotification(achievement);
        saveAchievements();
    }
}

function saveAchievements() {
    const unlockedIds = achievementList.filter(a => a.unlocked).map(a => a.id);
    localStorage.setItem('snakeAchievements', JSON.stringify(unlockedIds));
}

function showAchievementNotification(achievement) {
    // Create achievement popup
    const popup = document.createElement('div');
    popup.className = 'achievement-popup';
    popup.innerHTML = `
        <div class="achievement-content">
            <h3>🏆 إنجاز جديد!</h3>
            <h4>${achievement.name}</h4>
            <p>${achievement.description}</p>
        </div>
    `;
    document.body.appendChild(popup);

    // Play achievement sound
    playSound(523, 0.2);
    setTimeout(() => playSound(659, 0.2), 100);
    setTimeout(() => playSound(784, 0.3), 200);

    // Remove after 3 seconds
    setTimeout(() => {
        popup.remove();
    }, 3000);
}

// Enhanced food generation with different types
function generateFood() {
    let foodType = 'normal';
    const rand = Math.random();

    // Adjust probabilities based on level
    const levelMultiplier = Math.min(level / 5, 2);

    if (rand < 0.02 * levelMultiplier) foodType = 'rainbow';  // 2% chance, increases with level
    else if (rand < 0.05 * levelMultiplier) foodType = 'mega';      // 5% chance
    else if (rand < 0.08 && gameMode === 'challenge') foodType = 'bomb'; // 3% chance in challenge mode
    else if (rand < 0.15) foodType = 'magic'; // 10% chance
    else if (rand < 0.25) foodType = 'golden'; // 10% chance

    let newFood;
    do {
        newFood = {
            x: Math.floor(Math.random() * GRID_COUNT),
            y: Math.floor(Math.random() * GRID_COUNT),
            type: foodType,
            pulse: 0,
            sparkles: [],
            rotation: 0
        };
    } while (
        snake.some(segment => segment.x === newFood.x && segment.y === newFood.y) ||
        foods.some(food => food.x === newFood.x && food.y === newFood.y) ||
        obstacles.some(obs => obs.x === newFood.x && obs.y === newFood.y) ||
        portals.some(portal =>
            (portal.entrance.x === newFood.x && portal.entrance.y === newFood.y) ||
            (portal.exit.x === newFood.x && portal.exit.y === newFood.y)
        )
    );

    // Add sparkle particles for special foods
    if (foodType !== 'normal') {
        const sparkleCount = foodType === 'rainbow' ? 8 : 5;
        for (let i = 0; i < sparkleCount; i++) {
            newFood.sparkles.push({
                x: newFood.x * GRID_SIZE + GRID_SIZE/2,
                y: newFood.y * GRID_SIZE + GRID_SIZE/2,
                angle: (Math.PI * 2 / sparkleCount) * i,
                distance: 0,
                speed: Math.random() * 0.5 + 0.5
            });
        }
    }

    foods.push(newFood);

    // Ensure at least one normal food exists
    if (foods.length === 1 && foodType !== 'normal') {
        generateFood();
    }
}

function update() {
    if (gameState !== 'playing') return;

    // Update direction
    direction = { ...nextDirection };

    // Move snake
    const head = { ...snake[0] };
    head.x += direction.x;
    head.y += direction.y;
    head.smoothX = head.x * GRID_SIZE;
    head.smoothY = head.y * GRID_SIZE;

    // Handle wall collision based on game mode
    if (gameModes[gameMode].wallWrap) {
        // Infinite mode - wrap around walls
        if (head.x < 0) head.x = GRID_COUNT - 1;
        if (head.x >= GRID_COUNT) head.x = 0;
        if (head.y < 0) head.y = GRID_COUNT - 1;
        if (head.y >= GRID_COUNT) head.y = 0;
    } else {
        // Normal wall collision
        if (head.x < 0 || head.x >= GRID_COUNT || head.y < 0 || head.y >= GRID_COUNT) {
            gameOver();
            return;
        }
    }

    // Check self collision
    if (snake.some(segment => segment.x === head.x && segment.y === head.y)) {
        gameOver();
        return;
    }

    // Check obstacle collision
    if (obstacles.some(obs => Math.floor(obs.x) === head.x && obs.y === head.y)) {
        gameOver();
        return;
    }

    // Check portal collision
    portals.forEach(portal => {
        if (portal.entrance.x === head.x && portal.entrance.y === head.y && portal.cooldown === 0) {
            head.x = portal.exit.x;
            head.y = portal.exit.y;
            head.smoothX = head.x * GRID_SIZE;
            head.smoothY = head.y * GRID_SIZE;
            portal.cooldown = 10; // Prevent immediate re-teleport

            // Create teleport effect
            shockwaves.push(new Shockwave(
                portal.entrance.x * GRID_SIZE + GRID_SIZE/2,
                portal.entrance.y * GRID_SIZE + GRID_SIZE/2
            ));
            shockwaves.push(new Shockwave(
                portal.exit.x * GRID_SIZE + GRID_SIZE/2,
                portal.exit.y * GRID_SIZE + GRID_SIZE/2
            ));

            playSound(800, 0.2, 'sine');
        }
    });

    snake.unshift(head);

    // Check food collisions
    let foodEaten = false;
    foods.forEach((food, index) => {
        if (head.x === food.x && head.y === food.y) {
            const foodData = foodTypes[food.type];
            let points = foodData.points;

            // Handle bomb food
            if (food.type === 'bomb') {
                score = Math.max(0, score + points); // Negative points but don't go below 0
                combo = 0;

                // Create big explosion
                explosions.push(new Explosion(
                    food.x * GRID_SIZE + GRID_SIZE/2,
                    food.y * GRID_SIZE + GRID_SIZE/2,
                    '#FF0000'
                ));

                // Screen shake effect
                screenShake.intensity = 10;

                // Remove some snake segments
                if (snake.length > 3) {
                    snake.splice(-2, 2);
                }

                playSound(220, 0.5, 'sawtooth');
            } else {
                // Apply combo multiplier for positive foods
                if (combo > 0) {
                    points *= (1 + combo * 0.5);
                }

                score += Math.floor(points);
                combo++;

                // Create explosion effect
                explosions.push(new Explosion(
                    food.x * GRID_SIZE + GRID_SIZE/2,
                    food.y * GRID_SIZE + GRID_SIZE/2,
                    foodData.glow
                ));

                // Apply power-up effect
                if (foodData.effect) {
                    applyPowerUp(foodData.effect);
                }

                // Play sound based on food type
                switch (food.type) {
                    case 'normal':
                        playSound(440, 0.1);
                        break;
                    case 'golden':
                        playSound(660, 0.2, 'triangle');
                        break;
                    case 'magic':
                        playSound(880, 0.15, 'sawtooth');
                        break;
                    case 'mega':
                        playSound(1100, 0.3, 'square');
                        break;
                    case 'rainbow':
                        // Play rainbow sound sequence
                        playSound(523, 0.1);
                        setTimeout(() => playSound(659, 0.1), 50);
                        setTimeout(() => playSound(784, 0.1), 100);
                        break;
                }
            }

            foods.splice(index, 1);
            foodEaten = true;

            // Check achievements
            if (score >= 100) checkAchievement('score_100');
            if (score >= 500) checkAchievement('score_500');
            if (combo >= 10) checkAchievement('combo_10');
            if (food.type === 'rainbow') {
                const rainbowCount = parseInt(localStorage.getItem('rainbowEaten') || '0') + 1;
                localStorage.setItem('rainbowEaten', rainbowCount.toString());
                if (rainbowCount >= 5) checkAchievement('rainbow_eater');
            }

            // Level progression
            if (score > level * 100) {
                level++;
                gameSpeed = Math.max(80, gameSpeed - 10);
                clearInterval(gameLoop);
                gameLoop = setInterval(update, gameSpeed);

                if (level >= 10) checkAchievement('speed_demon');
            }
        }
    });

    if (!foodEaten) {
        snake.pop();
        combo = Math.max(0, combo - 1);
    }

    // Ensure there's always food available
    if (foods.length === 0) {
        generateFood();
    }

    updateScore();
}

// Power-up system
function applyPowerUp(effect) {
    const powerUp = {
        type: effect,
        startTime: Date.now(),
        duration: powerUpDuration
    };

    activePowerUps.push(powerUp);

    switch (effect) {
        case 'speed':
            gameSpeed = Math.max(50, gameSpeed - 30);
            clearInterval(gameLoop);
            gameLoop = setInterval(update, gameSpeed);
            break;
        case 'double':
            // Double points effect handled in scoring
            break;
        case 'mega':
            // Mega points already applied
            break;
        case 'rainbow':
            // Rainbow effect - change snake colors
            snake.forEach((segment, index) => {
                segment.rainbowHue = (index * 30) % 360;
            });

            // Add rainbow particles
            for (let i = 0; i < 20; i++) {
                particles.push(new Particle(
                    head.x * GRID_SIZE + GRID_SIZE/2,
                    head.y * GRID_SIZE + GRID_SIZE/2,
                    `hsl(${Math.random() * 360}, 80%, 60%)`,
                    Math.random() * 3 + 2
                ));
            }
            break;
    }
}

// Update power-ups
function updatePowerUps() {
    const currentTime = Date.now();
    activePowerUps = activePowerUps.filter(powerUp => {
        if (currentTime - powerUp.startTime > powerUp.duration) {
            // Remove power-up effect
            if (powerUp.type === 'speed') {
                gameSpeed = Math.min(150, gameSpeed + 30);
                clearInterval(gameLoop);
                gameLoop = setInterval(update, gameSpeed);
            }
            return false;
        }
        return true;
    });
}

// Animation loop for smooth effects
function startAnimationLoop() {
    function animate() {
        time += 0.016; // ~60fps

        // Update screen shake
        if (screenShake.intensity > 0) {
            screenShake.x = (Math.random() - 0.5) * screenShake.intensity;
            screenShake.y = (Math.random() - 0.5) * screenShake.intensity;
            screenShake.intensity *= 0.9;
            if (screenShake.intensity < 0.1) {
                screenShake.intensity = 0;
                screenShake.x = 0;
                screenShake.y = 0;
            }
        }

        // Update background particles
        backgroundParticles.forEach(particle => {
            particle.y -= particle.speed;
            if (particle.y < 0) {
                particle.y = CANVAS_SIZE;
                particle.x = Math.random() * CANVAS_SIZE;
            }
        });

        // Update neon rain
        neonRain.forEach(drop => {
            drop.y += drop.speed;
            if (drop.y > CANVAS_SIZE) {
                drop.y = -drop.length;
                drop.x = Math.random() * CANVAS_SIZE;
            }
        });

        // Update food sparkles and rotation
        foods.forEach(food => {
            food.pulse += 0.1;
            food.rotation += 0.05;
            food.sparkles.forEach(sparkle => {
                sparkle.distance = Math.sin(time * 2) * 10 + 15;
                sparkle.x = food.x * GRID_SIZE + GRID_SIZE/2 + Math.cos(sparkle.angle + time) * sparkle.distance;
                sparkle.y = food.y * GRID_SIZE + GRID_SIZE/2 + Math.sin(sparkle.angle + time) * sparkle.distance;
            });
        });

        // Update obstacles
        obstacles.forEach(obstacle => obstacle.update());

        // Update portals
        portals.forEach(portal => portal.update());

        // Update particles
        particles.forEach(particle => particle.update());
        particles = particles.filter(particle => particle.life > 0);

        // Update explosions
        explosions.forEach(explosion => explosion.update());
        explosions = explosions.filter(explosion => explosion.life > 0);

        // Update shockwaves
        shockwaves.forEach(shockwave => shockwave.update());
        shockwaves = shockwaves.filter(shockwave => shockwave.life > 0);

        // Update power-ups
        updatePowerUps();

        // Draw everything
        draw();

        animationFrame = requestAnimationFrame(animate);
    }
    animate();
}

function draw() {
    ctx.save();

    // Apply screen shake
    ctx.translate(screenShake.x, screenShake.y);

    // Create dynamic starfield background
    const gradient = ctx.createRadialGradient(CANVAS_SIZE/2, CANVAS_SIZE/2, 0, CANVAS_SIZE/2, CANVAS_SIZE/2, CANVAS_SIZE);
    gradient.addColorStop(0, `hsl(${Math.sin(time) * 30 + 220}, 50%, 10%)`);
    gradient.addColorStop(1, '#000000');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, CANVAS_SIZE, CANVAS_SIZE);

    // Draw neon rain
    neonRain.forEach(drop => {
        ctx.save();
        ctx.globalAlpha = drop.opacity;
        ctx.strokeStyle = drop.color;
        ctx.lineWidth = 2;
        ctx.shadowColor = drop.color;
        ctx.shadowBlur = 5;
        ctx.beginPath();
        ctx.moveTo(drop.x, drop.y);
        ctx.lineTo(drop.x, drop.y + drop.length);
        ctx.stroke();
        ctx.restore();
    });

    // Draw background particles (stars)
    backgroundParticles.forEach(particle => {
        ctx.save();
        ctx.globalAlpha = particle.opacity;
        ctx.fillStyle = '#FFFFFF';
        ctx.shadowColor = '#FFFFFF';
        ctx.shadowBlur = 3;
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.fill();
        ctx.restore();
    });

    // Draw snake with enhanced effects
    snake.forEach((segment, index) => {
        const x = segment.x * GRID_SIZE;
        const y = segment.y * GRID_SIZE;

        if (index === 0) {
            // Snake head with special glow and rainbow effect
            let headColor = '#00FF88';
            let headGlow = '#44FFAA';

            // Check for rainbow power-up
            const rainbowPowerUp = activePowerUps.find(p => p.type === 'rainbow');
            if (rainbowPowerUp) {
                headColor = `hsl(${(time * 100) % 360}, 80%, 60%)`;
                headGlow = `hsl(${(time * 100 + 60) % 360}, 80%, 80%)`;
            }

            drawGlowingRect(x, y, GRID_SIZE, headColor, headGlow);

            // Add animated eyes
            ctx.fillStyle = '#FFFFFF';
            ctx.shadowColor = '#FFFFFF';
            ctx.shadowBlur = 3;
            const eyeSize = 3;
            const eyeOffset = 6;
            const blinkOffset = Math.sin(time * 3) > 0.8 ? 2 : 0; // Blinking effect

            if (direction.x === 1) { // Right
                ctx.fillRect(x + GRID_SIZE - eyeOffset, y + 4 + blinkOffset, eyeSize, eyeSize - blinkOffset);
                ctx.fillRect(x + GRID_SIZE - eyeOffset, y + GRID_SIZE - 7, eyeSize, eyeSize - blinkOffset);
            } else if (direction.x === -1) { // Left
                ctx.fillRect(x + 3, y + 4 + blinkOffset, eyeSize, eyeSize - blinkOffset);
                ctx.fillRect(x + 3, y + GRID_SIZE - 7, eyeSize, eyeSize - blinkOffset);
            } else if (direction.y === -1) { // Up
                ctx.fillRect(x + 4 + blinkOffset, y + 3, eyeSize - blinkOffset, eyeSize);
                ctx.fillRect(x + GRID_SIZE - 7, y + 3, eyeSize - blinkOffset, eyeSize);
            } else if (direction.y === 1) { // Down
                ctx.fillRect(x + 4 + blinkOffset, y + GRID_SIZE - eyeOffset, eyeSize - blinkOffset, eyeSize);
                ctx.fillRect(x + GRID_SIZE - 7, y + GRID_SIZE - eyeOffset, eyeSize - blinkOffset, eyeSize);
            }
        } else {
            // Snake body with dynamic colors
            let intensity = 1 - (index / snake.length) * 0.5;
            let color, glowColor;

            // Check for rainbow power-up
            const rainbowPowerUp = activePowerUps.find(p => p.type === 'rainbow');
            if (rainbowPowerUp) {
                const hue = (time * 50 + index * 30) % 360;
                color = `hsl(${hue}, 80%, ${50 * intensity}%)`;
                glowColor = `hsl(${hue}, 80%, ${70 * intensity}%)`;
            } else {
                color = `hsl(${120 + index * 5}, 80%, ${50 * intensity}%)`;
                glowColor = `hsl(${120 + index * 5}, 80%, ${70 * intensity}%)`;
            }

            drawGlowingRect(x, y, GRID_SIZE, color, glowColor);

            // Add trailing particles for rainbow mode
            if (rainbowPowerUp && Math.random() < 0.3) {
                particles.push(new Particle(
                    x + GRID_SIZE/2,
                    y + GRID_SIZE/2,
                    color,
                    2
                ));
            }
        }
    });

    // Draw obstacles
    obstacles.forEach(obstacle => obstacle.draw());

    // Draw portals
    portals.forEach(portal => portal.draw());

    // Draw foods with enhanced effects
    foods.forEach(food => {
        const foodData = foodTypes[food.type];
        const x = food.x * GRID_SIZE;
        const y = food.y * GRID_SIZE;

        // Enhanced pulsing glow effect
        const pulseSize = Math.sin(food.pulse) * 3 + GRID_SIZE;
        const rotationOffset = food.type === 'rainbow' ? Math.sin(food.rotation) * 2 : 0;

        drawGlowingRect(x - 1.5 + rotationOffset, y - 1.5 + rotationOffset, pulseSize, foodData.color, foodData.glow);

        // Draw emoji for food types
        ctx.save();
        ctx.font = '16px Arial';
        ctx.textAlign = 'center';
        ctx.fillStyle = '#FFFFFF';
        ctx.shadowColor = foodData.glow;
        ctx.shadowBlur = 5;
        ctx.fillText(foodData.emoji, x + GRID_SIZE/2, y + GRID_SIZE/2 + 5);
        ctx.restore();

        // Draw sparkles for special foods
        if (food.type !== 'normal') {
            food.sparkles.forEach(sparkle => {
                ctx.save();
                ctx.globalAlpha = 0.8;
                ctx.fillStyle = foodData.glow;
                ctx.shadowColor = foodData.glow;
                ctx.shadowBlur = 3;
                ctx.beginPath();
                ctx.arc(sparkle.x, sparkle.y, 2, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            });
        }

        // Special rainbow food effect
        if (food.type === 'rainbow') {
            const rainbowRadius = Math.sin(food.pulse * 2) * 5 + 15;
            for (let i = 0; i < 8; i++) {
                const angle = (i / 8) * Math.PI * 2 + food.rotation;
                const rainbowX = x + GRID_SIZE/2 + Math.cos(angle) * rainbowRadius;
                const rainbowY = y + GRID_SIZE/2 + Math.sin(angle) * rainbowRadius;

                ctx.save();
                ctx.globalAlpha = 0.6;
                ctx.fillStyle = `hsl(${i * 45}, 80%, 60%)`;
                ctx.shadowColor = `hsl(${i * 45}, 80%, 60%)`;
                ctx.shadowBlur = 5;
                ctx.beginPath();
                ctx.arc(rainbowX, rainbowY, 3, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            }
        }
    });

    // Draw particles
    particles.forEach(particle => particle.draw());

    // Draw explosions
    explosions.forEach(explosion => explosion.draw());

    // Draw shockwaves
    shockwaves.forEach(shockwave => shockwave.draw());

    // Draw power-up indicators
    drawPowerUpIndicators();

    // Draw game mode indicator
    drawGameModeIndicator();

    ctx.restore();
}

function drawGlowingRect(x, y, size, color, glowColor) {
    ctx.save();

    // Outer glow
    ctx.shadowColor = glowColor;
    ctx.shadowBlur = 10;
    ctx.fillStyle = color;
    ctx.fillRect(x + 2, y + 2, size - 4, size - 4);

    // Inner highlight
    ctx.shadowBlur = 0;
    ctx.fillStyle = glowColor;
    ctx.fillRect(x + 4, y + 4, size - 8, size - 8);

    ctx.restore();
}

function drawPowerUpIndicators() {
    let yOffset = 10;
    activePowerUps.forEach(powerUp => {
        const timeLeft = powerUp.duration - (Date.now() - powerUp.startTime);
        const progress = timeLeft / powerUp.duration;

        ctx.save();
        ctx.fillStyle = `rgba(255, 255, 255, ${progress})`;
        ctx.font = '12px Arial';
        ctx.shadowColor = '#00FF88';
        ctx.shadowBlur = 3;

        let text = '';
        switch (powerUp.type) {
            case 'speed': text = '⚡ سرعة'; break;
            case 'double': text = '✨ نقاط مضاعفة'; break;
            case 'mega': text = '💎 ميجا'; break;
            case 'rainbow': text = '🌈 قوس قزح'; break;
        }

        ctx.fillText(text, 10, yOffset);

        // Enhanced progress bar
        ctx.fillStyle = `rgba(255, 255, 255, 0.3)`;
        ctx.fillRect(10, yOffset + 5, 100, 4);

        const gradient = ctx.createLinearGradient(10, 0, 110, 0);
        gradient.addColorStop(0, '#00FF88');
        gradient.addColorStop(1, '#00CCFF');
        ctx.fillStyle = gradient;
        ctx.fillRect(10, yOffset + 5, 100 * progress, 4);

        yOffset += 25;
        ctx.restore();
    });
}

function drawGameModeIndicator() {
    ctx.save();
    ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
    ctx.font = '14px Arial';
    ctx.shadowColor = '#00FF88';
    ctx.shadowBlur = 3;
    ctx.textAlign = 'right';
    ctx.fillText(`وضع: ${gameModes[gameMode].name}`, CANVAS_SIZE - 10, 20);
    ctx.restore();
}

function handleKeyPress(event) {
    if (event.code === 'Space') {
        event.preventDefault();
        if (gameState === 'menu' || gameState === 'gameOver') {
            startGame();
        } else if (gameState === 'playing') {
            pauseGame();
        } else if (gameState === 'paused') {
            resumeGame();
        }
        return;
    }
    
    if (gameState !== 'playing') return;
    
    switch (event.code) {
        case 'ArrowUp':
            event.preventDefault();
            handleDirectionChange('up');
            break;
        case 'ArrowDown':
            event.preventDefault();
            handleDirectionChange('down');
            break;
        case 'ArrowLeft':
            event.preventDefault();
            handleDirectionChange('left');
            break;
        case 'ArrowRight':
            event.preventDefault();
            handleDirectionChange('right');
            break;
    }
}

function handleDirectionChange(dir) {
    if (gameState !== 'playing') return;
    
    switch (dir) {
        case 'up':
            if (direction.y === 0) nextDirection = { x: 0, y: -1 };
            break;
        case 'down':
            if (direction.y === 0) nextDirection = { x: 0, y: 1 };
            break;
        case 'left':
            if (direction.x === 0) nextDirection = { x: -1, y: 0 };
            break;
        case 'right':
            if (direction.x === 0) nextDirection = { x: 1, y: 0 };
            break;
    }
}

function pauseGame() {
    gameState = 'paused';
    clearInterval(gameLoop);
    overlayTitle.textContent = 'اللعبة متوقفة';
    overlayMessage.textContent = 'اضغط مسافة للمتابعة';
    startButton.textContent = 'متابعة';
    showOverlay();
}

function resumeGame() {
    gameState = 'playing';
    hideOverlay();
    gameLoop = setInterval(update, 150);
}

function gameOver() {
    gameState = 'gameOver';
    clearInterval(gameLoop);

    // Play game over sound
    playSound(220, 0.5, 'sawtooth');
    setTimeout(() => playSound(110, 0.5, 'sawtooth'), 200);

    if (score > highScore) {
        highScore = score;
        localStorage.setItem('snakeHighScore', highScore);
        highScoreElement.textContent = highScore;
        overlayTitle.textContent = '🎉 رقم قياسي جديد!';
        overlayMessage.textContent = `تهانينا! حققت ${score} نقطة`;

        // Play victory sound
        setTimeout(() => {
            playSound(523, 0.2);
            setTimeout(() => playSound(659, 0.2), 100);
            setTimeout(() => playSound(784, 0.3), 200);
        }, 500);
    } else {
        overlayTitle.textContent = 'انتهت اللعبة';
        overlayMessage.textContent = `النقاط: ${score}`;
    }

    startButton.textContent = 'العب مرة أخرى';
    showOverlay();
}

function updateScore() {
    scoreElement.textContent = `${score} (المستوى ${level})`;
    if (combo > 1) {
        scoreElement.textContent += ` | كومبو ×${combo}`;
    }
}

function updateOverlay() {
    if (gameState === 'menu') {
        overlayTitle.textContent = 'اضغط مسافة للبدء';
        overlayMessage.textContent = 'استخدم الأسهم للتحكم في الثعبان';
        startButton.textContent = 'ابدأ اللعبة';
    }
}

function showOverlay() {
    gameOverlay.classList.remove('hidden');
}

function hideOverlay() {
    gameOverlay.classList.add('hidden');
}

// Sound toggle function
function toggleSound() {
    soundEnabled = !soundEnabled;
    soundToggle.textContent = soundEnabled ? '🔊' : '🔇';
    soundToggle.classList.toggle('muted', !soundEnabled);
}

// Make selectMode global for HTML onclick
window.selectMode = selectMode;

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', init);
