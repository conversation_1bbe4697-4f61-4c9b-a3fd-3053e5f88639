// Game variables
const canvas = document.getElementById('gameCanvas');
const ctx = canvas.getContext('2d');
const scoreElement = document.getElementById('score');
const highScoreElement = document.getElementById('highScore');
const gameOverlay = document.getElementById('gameOverlay');
const overlayTitle = document.getElementById('overlayTitle');
const overlayMessage = document.getElementById('overlayMessage');
const startButton = document.getElementById('startButton');
const soundToggle = document.getElementById('soundToggle');

// Game settings
const GRID_SIZE = 20;
const CANVAS_SIZE = 400;
const GRID_COUNT = CANVAS_SIZE / GRID_SIZE;

// Game state
let gameState = 'menu'; // 'menu', 'playing', 'paused', 'gameOver'
let score = 0;
let highScore = localStorage.getItem('snakeHighScore') || 0;
let gameLoop;
let animationFrame;
let gameSpeed = 150;
let level = 1;
let combo = 0;

// Snake with smooth movement
let snake = [
    { x: 10, y: 10, smoothX: 10 * GRID_SIZE, smoothY: 10 * GRID_SIZE }
];
let direction = { x: 0, y: 0 };
let nextDirection = { x: 0, y: 0 };

// Enhanced Food System
let foods = [];
let foodTypes = {
    normal: { color: '#FF4444', points: 10, effect: null, glow: '#FF6666' },
    golden: { color: '#FFD700', points: 50, effect: 'speed', glow: '#FFFF44' },
    magic: { color: '#9C27B0', points: 25, effect: 'double', glow: '#E1BEE7' },
    mega: { color: '#00BCD4', points: 100, effect: 'mega', glow: '#4DD0E1' }
};

// Particle System
let particles = [];
let backgroundParticles = [];

// Power-ups and Effects
let activePowerUps = [];
let powerUpDuration = 5000; // 5 seconds

// Animation and Visual Effects
let time = 0;
let explosions = [];

// Audio System
let audioContext;
let soundEnabled = true;

// Initialize audio
function initAudio() {
    try {
        audioContext = new (window.AudioContext || window.webkitAudioContext)();
    } catch (e) {
        console.log('Web Audio API not supported');
        soundEnabled = false;
    }
}

// Play sound effect
function playSound(frequency, duration, type = 'sine') {
    if (!soundEnabled || !audioContext) return;

    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
    oscillator.type = type;

    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + duration);
}

// Particle Class
class Particle {
    constructor(x, y, color, size = 3, velocity = null) {
        this.x = x;
        this.y = y;
        this.color = color;
        this.size = size;
        this.life = 1.0;
        this.decay = Math.random() * 0.02 + 0.01;
        this.velocity = velocity || {
            x: (Math.random() - 0.5) * 4,
            y: (Math.random() - 0.5) * 4
        };
    }

    update() {
        this.x += this.velocity.x;
        this.y += this.velocity.y;
        this.life -= this.decay;
        this.velocity.x *= 0.98;
        this.velocity.y *= 0.98;
    }

    draw() {
        ctx.save();
        ctx.globalAlpha = this.life;
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.size * this.life, 0, Math.PI * 2);
        ctx.fill();
        ctx.restore();
    }
}

// Explosion Effect
class Explosion {
    constructor(x, y, color) {
        this.x = x;
        this.y = y;
        this.particles = [];
        for (let i = 0; i < 15; i++) {
            this.particles.push(new Particle(x, y, color, Math.random() * 5 + 2));
        }
        this.life = 1.0;
    }

    update() {
        this.particles.forEach(particle => particle.update());
        this.particles = this.particles.filter(particle => particle.life > 0);
        this.life -= 0.05;
    }

    draw() {
        this.particles.forEach(particle => particle.draw());
    }
}

// Initialize game
function init() {
    highScoreElement.textContent = highScore;
    updateOverlay();
    generateFood();
    initBackgroundParticles();
    initAudio();
    startAnimationLoop();

    // Event listeners
    startButton.addEventListener('click', startGame);
    document.addEventListener('keydown', handleKeyPress);
    soundToggle.addEventListener('click', toggleSound);

    // Touch controls for mobile
    const controlBtns = document.querySelectorAll('.control-btn');
    controlBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            const dir = btn.dataset.direction;
            handleDirectionChange(dir);
        });
    });
}

// Background particles for ambiance
function initBackgroundParticles() {
    for (let i = 0; i < 50; i++) {
        backgroundParticles.push({
            x: Math.random() * CANVAS_SIZE,
            y: Math.random() * CANVAS_SIZE,
            size: Math.random() * 2 + 1,
            speed: Math.random() * 0.5 + 0.1,
            opacity: Math.random() * 0.5 + 0.1
        });
    }
}

function startGame() {
    gameState = 'playing';
    score = 0;
    level = 1;
    combo = 0;
    gameSpeed = 150;
    snake = [{ x: 10, y: 10, smoothX: 10 * GRID_SIZE, smoothY: 10 * GRID_SIZE }];
    direction = { x: 1, y: 0 };
    nextDirection = { x: 1, y: 0 };
    foods = [];
    particles = [];
    explosions = [];
    activePowerUps = [];
    generateFood();
    updateScore();
    hideOverlay();

    if (gameLoop) clearInterval(gameLoop);
    gameLoop = setInterval(update, gameSpeed);
}

// Enhanced food generation with different types
function generateFood() {
    let foodType = 'normal';
    const rand = Math.random();

    if (rand < 0.05) foodType = 'mega';      // 5% chance
    else if (rand < 0.15) foodType = 'magic'; // 10% chance
    else if (rand < 0.25) foodType = 'golden'; // 10% chance

    let newFood;
    do {
        newFood = {
            x: Math.floor(Math.random() * GRID_COUNT),
            y: Math.floor(Math.random() * GRID_COUNT),
            type: foodType,
            pulse: 0,
            sparkles: []
        };
    } while (snake.some(segment => segment.x === newFood.x && segment.y === newFood.y) ||
             foods.some(food => food.x === newFood.x && food.y === newFood.y));

    // Add sparkle particles for special foods
    if (foodType !== 'normal') {
        for (let i = 0; i < 5; i++) {
            newFood.sparkles.push({
                x: newFood.x * GRID_SIZE + GRID_SIZE/2,
                y: newFood.y * GRID_SIZE + GRID_SIZE/2,
                angle: (Math.PI * 2 / 5) * i,
                distance: 0,
                speed: Math.random() * 0.5 + 0.5
            });
        }
    }

    foods.push(newFood);

    // Ensure at least one normal food exists
    if (foods.length === 1 && foodType !== 'normal') {
        generateFood();
    }
}

function update() {
    if (gameState !== 'playing') return;

    // Update direction
    direction = { ...nextDirection };

    // Move snake
    const head = { ...snake[0] };
    head.x += direction.x;
    head.y += direction.y;
    head.smoothX = head.x * GRID_SIZE;
    head.smoothY = head.y * GRID_SIZE;

    // Check wall collision
    if (head.x < 0 || head.x >= GRID_COUNT || head.y < 0 || head.y >= GRID_COUNT) {
        gameOver();
        return;
    }

    // Check self collision
    if (snake.some(segment => segment.x === head.x && segment.y === head.y)) {
        gameOver();
        return;
    }

    snake.unshift(head);

    // Check food collisions
    let foodEaten = false;
    foods.forEach((food, index) => {
        if (head.x === food.x && head.y === food.y) {
            const foodData = foodTypes[food.type];
            let points = foodData.points;

            // Apply combo multiplier
            if (combo > 0) {
                points *= (1 + combo * 0.5);
            }

            score += Math.floor(points);
            combo++;

            // Create explosion effect
            explosions.push(new Explosion(
                food.x * GRID_SIZE + GRID_SIZE/2,
                food.y * GRID_SIZE + GRID_SIZE/2,
                foodData.glow
            ));

            // Apply power-up effect
            if (foodData.effect) {
                applyPowerUp(foodData.effect);
            }

            // Play sound based on food type
            switch (food.type) {
                case 'normal':
                    playSound(440, 0.1);
                    break;
                case 'golden':
                    playSound(660, 0.2, 'triangle');
                    break;
                case 'magic':
                    playSound(880, 0.15, 'sawtooth');
                    break;
                case 'mega':
                    playSound(1100, 0.3, 'square');
                    break;
            }

            foods.splice(index, 1);
            foodEaten = true;

            // Level progression
            if (score > level * 100) {
                level++;
                gameSpeed = Math.max(80, gameSpeed - 10);
                clearInterval(gameLoop);
                gameLoop = setInterval(update, gameSpeed);
            }
        }
    });

    if (!foodEaten) {
        snake.pop();
        combo = Math.max(0, combo - 1);
    }

    // Ensure there's always food available
    if (foods.length === 0) {
        generateFood();
    }

    updateScore();
}

// Power-up system
function applyPowerUp(effect) {
    const powerUp = {
        type: effect,
        startTime: Date.now(),
        duration: powerUpDuration
    };

    activePowerUps.push(powerUp);

    switch (effect) {
        case 'speed':
            gameSpeed = Math.max(50, gameSpeed - 30);
            clearInterval(gameLoop);
            gameLoop = setInterval(update, gameSpeed);
            break;
        case 'double':
            // Double points effect handled in scoring
            break;
        case 'mega':
            // Mega points already applied
            break;
    }
}

// Update power-ups
function updatePowerUps() {
    const currentTime = Date.now();
    activePowerUps = activePowerUps.filter(powerUp => {
        if (currentTime - powerUp.startTime > powerUp.duration) {
            // Remove power-up effect
            if (powerUp.type === 'speed') {
                gameSpeed = Math.min(150, gameSpeed + 30);
                clearInterval(gameLoop);
                gameLoop = setInterval(update, gameSpeed);
            }
            return false;
        }
        return true;
    });
}

// Animation loop for smooth effects
function startAnimationLoop() {
    function animate() {
        time += 0.016; // ~60fps

        // Update background particles
        backgroundParticles.forEach(particle => {
            particle.y -= particle.speed;
            if (particle.y < 0) {
                particle.y = CANVAS_SIZE;
                particle.x = Math.random() * CANVAS_SIZE;
            }
        });

        // Update food sparkles
        foods.forEach(food => {
            food.pulse += 0.1;
            food.sparkles.forEach(sparkle => {
                sparkle.distance = Math.sin(time * 2) * 10 + 15;
                sparkle.x = food.x * GRID_SIZE + GRID_SIZE/2 + Math.cos(sparkle.angle + time) * sparkle.distance;
                sparkle.y = food.y * GRID_SIZE + GRID_SIZE/2 + Math.sin(sparkle.angle + time) * sparkle.distance;
            });
        });

        // Update particles
        particles.forEach(particle => particle.update());
        particles = particles.filter(particle => particle.life > 0);

        // Update explosions
        explosions.forEach(explosion => explosion.update());
        explosions = explosions.filter(explosion => explosion.life > 0);

        // Update power-ups
        updatePowerUps();

        // Draw everything
        draw();

        animationFrame = requestAnimationFrame(animate);
    }
    animate();
}

function draw() {
    // Create starfield background
    const gradient = ctx.createRadialGradient(CANVAS_SIZE/2, CANVAS_SIZE/2, 0, CANVAS_SIZE/2, CANVAS_SIZE/2, CANVAS_SIZE);
    gradient.addColorStop(0, '#001122');
    gradient.addColorStop(1, '#000000');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, CANVAS_SIZE, CANVAS_SIZE);

    // Draw background particles (stars)
    backgroundParticles.forEach(particle => {
        ctx.save();
        ctx.globalAlpha = particle.opacity;
        ctx.fillStyle = '#FFFFFF';
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.fill();
        ctx.restore();
    });

    // Draw snake with glow effect
    snake.forEach((segment, index) => {
        const x = segment.x * GRID_SIZE;
        const y = segment.y * GRID_SIZE;

        if (index === 0) {
            // Snake head with special glow
            drawGlowingRect(x, y, GRID_SIZE, '#00FF88', '#44FFAA');

            // Add eyes
            ctx.fillStyle = '#FFFFFF';
            const eyeSize = 3;
            const eyeOffset = 6;
            if (direction.x === 1) { // Right
                ctx.fillRect(x + GRID_SIZE - eyeOffset, y + 4, eyeSize, eyeSize);
                ctx.fillRect(x + GRID_SIZE - eyeOffset, y + GRID_SIZE - 7, eyeSize, eyeSize);
            } else if (direction.x === -1) { // Left
                ctx.fillRect(x + 3, y + 4, eyeSize, eyeSize);
                ctx.fillRect(x + 3, y + GRID_SIZE - 7, eyeSize, eyeSize);
            } else if (direction.y === -1) { // Up
                ctx.fillRect(x + 4, y + 3, eyeSize, eyeSize);
                ctx.fillRect(x + GRID_SIZE - 7, y + 3, eyeSize, eyeSize);
            } else if (direction.y === 1) { // Down
                ctx.fillRect(x + 4, y + GRID_SIZE - eyeOffset, eyeSize, eyeSize);
                ctx.fillRect(x + GRID_SIZE - 7, y + GRID_SIZE - eyeOffset, eyeSize, eyeSize);
            }
        } else {
            // Snake body with gradient
            const intensity = 1 - (index / snake.length) * 0.5;
            const color = `hsl(${120 + index * 5}, 80%, ${50 * intensity}%)`;
            const glowColor = `hsl(${120 + index * 5}, 80%, ${70 * intensity}%)`;
            drawGlowingRect(x, y, GRID_SIZE, color, glowColor);
        }
    });

    // Draw foods with special effects
    foods.forEach(food => {
        const foodData = foodTypes[food.type];
        const x = food.x * GRID_SIZE;
        const y = food.y * GRID_SIZE;

        // Pulsing glow effect
        const pulseSize = Math.sin(food.pulse) * 3 + GRID_SIZE;
        drawGlowingRect(x - 1.5, y - 1.5, pulseSize, foodData.color, foodData.glow);

        // Draw sparkles for special foods
        if (food.type !== 'normal') {
            food.sparkles.forEach(sparkle => {
                ctx.save();
                ctx.globalAlpha = 0.8;
                ctx.fillStyle = foodData.glow;
                ctx.beginPath();
                ctx.arc(sparkle.x, sparkle.y, 2, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            });
        }
    });

    // Draw particles
    particles.forEach(particle => particle.draw());

    // Draw explosions
    explosions.forEach(explosion => explosion.draw());

    // Draw power-up indicators
    drawPowerUpIndicators();
}

function drawGlowingRect(x, y, size, color, glowColor) {
    ctx.save();

    // Outer glow
    ctx.shadowColor = glowColor;
    ctx.shadowBlur = 10;
    ctx.fillStyle = color;
    ctx.fillRect(x + 2, y + 2, size - 4, size - 4);

    // Inner highlight
    ctx.shadowBlur = 0;
    ctx.fillStyle = glowColor;
    ctx.fillRect(x + 4, y + 4, size - 8, size - 8);

    ctx.restore();
}

function drawPowerUpIndicators() {
    let yOffset = 10;
    activePowerUps.forEach(powerUp => {
        const timeLeft = powerUp.duration - (Date.now() - powerUp.startTime);
        const progress = timeLeft / powerUp.duration;

        ctx.save();
        ctx.fillStyle = `rgba(255, 255, 255, ${progress})`;
        ctx.font = '12px Arial';

        let text = '';
        switch (powerUp.type) {
            case 'speed': text = '⚡ سرعة'; break;
            case 'double': text = '✨ نقاط مضاعفة'; break;
            case 'mega': text = '💎 ميجا'; break;
        }

        ctx.fillText(text, 10, yOffset);

        // Progress bar
        ctx.fillStyle = `rgba(255, 255, 255, 0.3)`;
        ctx.fillRect(10, yOffset + 5, 100, 3);
        ctx.fillStyle = `rgba(0, 255, 136, ${progress})`;
        ctx.fillRect(10, yOffset + 5, 100 * progress, 3);

        yOffset += 25;
        ctx.restore();
    });
}

function handleKeyPress(event) {
    if (event.code === 'Space') {
        event.preventDefault();
        if (gameState === 'menu' || gameState === 'gameOver') {
            startGame();
        } else if (gameState === 'playing') {
            pauseGame();
        } else if (gameState === 'paused') {
            resumeGame();
        }
        return;
    }
    
    if (gameState !== 'playing') return;
    
    switch (event.code) {
        case 'ArrowUp':
            event.preventDefault();
            handleDirectionChange('up');
            break;
        case 'ArrowDown':
            event.preventDefault();
            handleDirectionChange('down');
            break;
        case 'ArrowLeft':
            event.preventDefault();
            handleDirectionChange('left');
            break;
        case 'ArrowRight':
            event.preventDefault();
            handleDirectionChange('right');
            break;
    }
}

function handleDirectionChange(dir) {
    if (gameState !== 'playing') return;
    
    switch (dir) {
        case 'up':
            if (direction.y === 0) nextDirection = { x: 0, y: -1 };
            break;
        case 'down':
            if (direction.y === 0) nextDirection = { x: 0, y: 1 };
            break;
        case 'left':
            if (direction.x === 0) nextDirection = { x: -1, y: 0 };
            break;
        case 'right':
            if (direction.x === 0) nextDirection = { x: 1, y: 0 };
            break;
    }
}

function pauseGame() {
    gameState = 'paused';
    clearInterval(gameLoop);
    overlayTitle.textContent = 'اللعبة متوقفة';
    overlayMessage.textContent = 'اضغط مسافة للمتابعة';
    startButton.textContent = 'متابعة';
    showOverlay();
}

function resumeGame() {
    gameState = 'playing';
    hideOverlay();
    gameLoop = setInterval(update, 150);
}

function gameOver() {
    gameState = 'gameOver';
    clearInterval(gameLoop);

    // Play game over sound
    playSound(220, 0.5, 'sawtooth');
    setTimeout(() => playSound(110, 0.5, 'sawtooth'), 200);

    if (score > highScore) {
        highScore = score;
        localStorage.setItem('snakeHighScore', highScore);
        highScoreElement.textContent = highScore;
        overlayTitle.textContent = '🎉 رقم قياسي جديد!';
        overlayMessage.textContent = `تهانينا! حققت ${score} نقطة`;

        // Play victory sound
        setTimeout(() => {
            playSound(523, 0.2);
            setTimeout(() => playSound(659, 0.2), 100);
            setTimeout(() => playSound(784, 0.3), 200);
        }, 500);
    } else {
        overlayTitle.textContent = 'انتهت اللعبة';
        overlayMessage.textContent = `النقاط: ${score}`;
    }

    startButton.textContent = 'العب مرة أخرى';
    showOverlay();
}

function updateScore() {
    scoreElement.textContent = `${score} (المستوى ${level})`;
    if (combo > 1) {
        scoreElement.textContent += ` | كومبو ×${combo}`;
    }
}

function updateOverlay() {
    if (gameState === 'menu') {
        overlayTitle.textContent = 'اضغط مسافة للبدء';
        overlayMessage.textContent = 'استخدم الأسهم للتحكم في الثعبان';
        startButton.textContent = 'ابدأ اللعبة';
    }
}

function showOverlay() {
    gameOverlay.classList.remove('hidden');
}

function hideOverlay() {
    gameOverlay.classList.add('hidden');
}

// Sound toggle function
function toggleSound() {
    soundEnabled = !soundEnabled;
    soundToggle.textContent = soundEnabled ? '🔊' : '🔇';
    soundToggle.classList.toggle('muted', !soundEnabled);
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', init);
