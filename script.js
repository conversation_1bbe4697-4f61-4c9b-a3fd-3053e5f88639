// Game variables
const canvas = document.getElementById('gameCanvas');
const ctx = canvas.getContext('2d');
const scoreElement = document.getElementById('score');
const highScoreElement = document.getElementById('highScore');
const gameOverlay = document.getElementById('gameOverlay');
const overlayTitle = document.getElementById('overlayTitle');
const overlayMessage = document.getElementById('overlayMessage');
const startButton = document.getElementById('startButton');

// Game settings
const GRID_SIZE = 20;
const CANVAS_SIZE = 400;
const GRID_COUNT = CANVAS_SIZE / GRID_SIZE;

// Game state
let gameState = 'menu'; // 'menu', 'playing', 'paused', 'gameOver'
let score = 0;
let highScore = localStorage.getItem('snakeHighScore') || 0;
let gameLoop;

// Snake
let snake = [
    { x: 10, y: 10 }
];
let direction = { x: 0, y: 0 };
let nextDirection = { x: 0, y: 0 };

// Food
let food = { x: 15, y: 15 };

// Initialize game
function init() {
    highScoreElement.textContent = highScore;
    updateOverlay();
    generateFood();
    draw();
    
    // Event listeners
    startButton.addEventListener('click', startGame);
    document.addEventListener('keydown', handleKeyPress);
    
    // Touch controls for mobile
    const controlBtns = document.querySelectorAll('.control-btn');
    controlBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            const dir = btn.dataset.direction;
            handleDirectionChange(dir);
        });
    });
}

function startGame() {
    gameState = 'playing';
    score = 0;
    snake = [{ x: 10, y: 10 }];
    direction = { x: 1, y: 0 };
    nextDirection = { x: 1, y: 0 };
    generateFood();
    updateScore();
    hideOverlay();
    
    if (gameLoop) clearInterval(gameLoop);
    gameLoop = setInterval(update, 150);
}

function update() {
    if (gameState !== 'playing') return;
    
    // Update direction
    direction = { ...nextDirection };
    
    // Move snake
    const head = { ...snake[0] };
    head.x += direction.x;
    head.y += direction.y;
    
    // Check wall collision
    if (head.x < 0 || head.x >= GRID_COUNT || head.y < 0 || head.y >= GRID_COUNT) {
        gameOver();
        return;
    }
    
    // Check self collision
    if (snake.some(segment => segment.x === head.x && segment.y === head.y)) {
        gameOver();
        return;
    }
    
    snake.unshift(head);
    
    // Check food collision
    if (head.x === food.x && head.y === food.y) {
        score += 10;
        updateScore();
        generateFood();
    } else {
        snake.pop();
    }
    
    draw();
}

function generateFood() {
    do {
        food.x = Math.floor(Math.random() * GRID_COUNT);
        food.y = Math.floor(Math.random() * GRID_COUNT);
    } while (snake.some(segment => segment.x === food.x && segment.y === food.y));
}

function draw() {
    // Clear canvas
    ctx.fillStyle = '#000';
    ctx.fillRect(0, 0, CANVAS_SIZE, CANVAS_SIZE);
    
    // Draw snake
    ctx.fillStyle = '#4CAF50';
    snake.forEach((segment, index) => {
        if (index === 0) {
            // Snake head
            ctx.fillStyle = '#66BB6A';
        } else {
            ctx.fillStyle = '#4CAF50';
        }
        ctx.fillRect(
            segment.x * GRID_SIZE + 1,
            segment.y * GRID_SIZE + 1,
            GRID_SIZE - 2,
            GRID_SIZE - 2
        );
    });
    
    // Draw food
    ctx.fillStyle = '#F44336';
    ctx.fillRect(
        food.x * GRID_SIZE + 1,
        food.y * GRID_SIZE + 1,
        GRID_SIZE - 2,
        GRID_SIZE - 2
    );
    
    // Add some style to food
    ctx.fillStyle = '#FF5722';
    ctx.fillRect(
        food.x * GRID_SIZE + 4,
        food.y * GRID_SIZE + 4,
        GRID_SIZE - 8,
        GRID_SIZE - 8
    );
}

function handleKeyPress(event) {
    if (event.code === 'Space') {
        event.preventDefault();
        if (gameState === 'menu' || gameState === 'gameOver') {
            startGame();
        } else if (gameState === 'playing') {
            pauseGame();
        } else if (gameState === 'paused') {
            resumeGame();
        }
        return;
    }
    
    if (gameState !== 'playing') return;
    
    switch (event.code) {
        case 'ArrowUp':
            event.preventDefault();
            handleDirectionChange('up');
            break;
        case 'ArrowDown':
            event.preventDefault();
            handleDirectionChange('down');
            break;
        case 'ArrowLeft':
            event.preventDefault();
            handleDirectionChange('left');
            break;
        case 'ArrowRight':
            event.preventDefault();
            handleDirectionChange('right');
            break;
    }
}

function handleDirectionChange(dir) {
    if (gameState !== 'playing') return;
    
    switch (dir) {
        case 'up':
            if (direction.y === 0) nextDirection = { x: 0, y: -1 };
            break;
        case 'down':
            if (direction.y === 0) nextDirection = { x: 0, y: 1 };
            break;
        case 'left':
            if (direction.x === 0) nextDirection = { x: -1, y: 0 };
            break;
        case 'right':
            if (direction.x === 0) nextDirection = { x: 1, y: 0 };
            break;
    }
}

function pauseGame() {
    gameState = 'paused';
    clearInterval(gameLoop);
    overlayTitle.textContent = 'اللعبة متوقفة';
    overlayMessage.textContent = 'اضغط مسافة للمتابعة';
    startButton.textContent = 'متابعة';
    showOverlay();
}

function resumeGame() {
    gameState = 'playing';
    hideOverlay();
    gameLoop = setInterval(update, 150);
}

function gameOver() {
    gameState = 'gameOver';
    clearInterval(gameLoop);
    
    if (score > highScore) {
        highScore = score;
        localStorage.setItem('snakeHighScore', highScore);
        highScoreElement.textContent = highScore;
        overlayTitle.textContent = '🎉 رقم قياسي جديد!';
        overlayMessage.textContent = `تهانينا! حققت ${score} نقطة`;
    } else {
        overlayTitle.textContent = 'انتهت اللعبة';
        overlayMessage.textContent = `النقاط: ${score}`;
    }
    
    startButton.textContent = 'العب مرة أخرى';
    showOverlay();
}

function updateScore() {
    scoreElement.textContent = score;
}

function updateOverlay() {
    if (gameState === 'menu') {
        overlayTitle.textContent = 'اضغط مسافة للبدء';
        overlayMessage.textContent = 'استخدم الأسهم للتحكم في الثعبان';
        startButton.textContent = 'ابدأ اللعبة';
    }
}

function showOverlay() {
    gameOverlay.classList.remove('hidden');
}

function hideOverlay() {
    gameOverlay.classList.add('hidden');
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', init);
