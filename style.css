* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
    overflow: hidden;
    position: relative;
}

/* Animated background */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
    animation: backgroundShift 20s ease-in-out infinite;
    z-index: -1;
}

@keyframes backgroundShift {
    0%, 100% { transform: translateX(0) translateY(0); }
    25% { transform: translateX(-20px) translateY(-10px); }
    50% { transform: translateX(20px) translateY(10px); }
    75% { transform: translateX(-10px) translateY(20px); }
}

.container {
    text-align: center;
    max-width: 600px;
    width: 100%;
    padding: 20px;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    text-shadow:
        0 0 10px rgba(0, 255, 136, 0.5),
        0 0 20px rgba(0, 255, 136, 0.3),
        0 0 30px rgba(0, 255, 136, 0.2);
    animation: titleGlow 3s ease-in-out infinite alternate;
}

@keyframes titleGlow {
    from {
        text-shadow:
            0 0 10px rgba(0, 255, 136, 0.5),
            0 0 20px rgba(0, 255, 136, 0.3),
            0 0 30px rgba(0, 255, 136, 0.2);
    }
    to {
        text-shadow:
            0 0 15px rgba(0, 255, 136, 0.8),
            0 0 25px rgba(0, 255, 136, 0.5),
            0 0 35px rgba(0, 255, 136, 0.3);
    }
}

.score-board {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    background: rgba(255,255,255,0.1);
    padding: 15px;
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

.sound-toggle {
    background: rgba(255,255,255,0.2);
    border: 2px solid rgba(0, 255, 136, 0.5);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.sound-toggle:hover {
    background: rgba(0, 255, 136, 0.3);
    transform: scale(1.1);
}

.sound-toggle.muted {
    opacity: 0.5;
}

.score, .high-score {
    font-size: 1.2rem;
    font-weight: bold;
}

.game-container {
    position: relative;
    display: inline-block;
    margin-bottom: 30px;
}

#gameCanvas {
    border: 4px solid rgba(0, 255, 136, 0.6);
    border-radius: 15px;
    background: #000;
    box-shadow:
        0 0 20px rgba(0, 255, 136, 0.3),
        0 8px 32px rgba(0,0,0,0.5),
        inset 0 0 20px rgba(0, 255, 136, 0.1);
    animation: canvasGlow 4s ease-in-out infinite alternate;
}

@keyframes canvasGlow {
    from {
        box-shadow:
            0 0 20px rgba(0, 255, 136, 0.3),
            0 8px 32px rgba(0,0,0,0.5),
            inset 0 0 20px rgba(0, 255, 136, 0.1);
    }
    to {
        box-shadow:
            0 0 30px rgba(0, 255, 136, 0.5),
            0 8px 32px rgba(0,0,0,0.5),
            inset 0 0 30px rgba(0, 255, 136, 0.2);
    }
}

.game-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 10px;
    backdrop-filter: blur(5px);
}

.overlay-content {
    text-align: center;
    color: white;
}

.overlay-content h2 {
    font-size: 2rem;
    margin-bottom: 15px;
}

.overlay-content p {
    font-size: 1.1rem;
    margin-bottom: 20px;
    opacity: 0.8;
}

.game-button {
    background: linear-gradient(45deg, #00ff88, #00cc6a);
    color: white;
    border: none;
    padding: 15px 30px;
    font-size: 1.1rem;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: bold;
    box-shadow:
        0 0 20px rgba(0, 255, 136, 0.3),
        0 4px 15px rgba(0,0,0,0.2);
    position: relative;
    overflow: hidden;
}

.game-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.game-button:hover::before {
    left: 100%;
}

.game-button:hover {
    transform: translateY(-3px);
    box-shadow:
        0 0 30px rgba(0, 255, 136, 0.5),
        0 8px 25px rgba(0,0,0,0.3);
}

.game-button:active {
    transform: translateY(-1px);
}

.controls {
    margin-bottom: 30px;
}

.controls h3 {
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.control-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    max-width: 150px;
    margin: 0 auto 15px;
}

.control-btn {
    width: 40px;
    height: 40px;
    background: rgba(255,255,255,0.2);
    border: 2px solid rgba(255,255,255,0.3);
    border-radius: 8px;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.control-btn:hover {
    background: rgba(255,255,255,0.3);
    transform: scale(1.1);
}

.control-btn:active {
    transform: scale(0.95);
}

.instructions {
    background: rgba(255,255,255,0.1);
    padding: 20px;
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

.instructions h3 {
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.instructions ul {
    list-style: none;
    text-align: right;
}

.instructions li {
    margin-bottom: 8px;
    padding: 5px 0;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.instructions li:last-child {
    border-bottom: none;
}

.hidden {
    display: none !important;
}

/* Responsive Design */
@media (max-width: 480px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    #gameCanvas {
        width: 300px;
        height: 300px;
    }
    
    .score-board {
        flex-direction: column;
        gap: 10px;
    }
    
    .overlay-content h2 {
        font-size: 1.5rem;
    }
}
