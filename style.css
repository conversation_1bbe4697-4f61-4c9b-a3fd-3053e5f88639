* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
}

.container {
    text-align: center;
    max-width: 600px;
    width: 100%;
    padding: 20px;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.score-board {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    background: rgba(255,255,255,0.1);
    padding: 15px;
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

.score, .high-score {
    font-size: 1.2rem;
    font-weight: bold;
}

.game-container {
    position: relative;
    display: inline-block;
    margin-bottom: 30px;
}

#gameCanvas {
    border: 4px solid #fff;
    border-radius: 10px;
    background: #000;
    box-shadow: 0 8px 32px rgba(0,0,0,0.3);
}

.game-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 10px;
    backdrop-filter: blur(5px);
}

.overlay-content {
    text-align: center;
    color: white;
}

.overlay-content h2 {
    font-size: 2rem;
    margin-bottom: 15px;
}

.overlay-content p {
    font-size: 1.1rem;
    margin-bottom: 20px;
    opacity: 0.8;
}

.game-button {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
    border: none;
    padding: 15px 30px;
    font-size: 1.1rem;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: bold;
}

.game-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255,107,107,0.4);
}

.controls {
    margin-bottom: 30px;
}

.controls h3 {
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.control-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    max-width: 150px;
    margin: 0 auto 15px;
}

.control-btn {
    width: 40px;
    height: 40px;
    background: rgba(255,255,255,0.2);
    border: 2px solid rgba(255,255,255,0.3);
    border-radius: 8px;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.control-btn:hover {
    background: rgba(255,255,255,0.3);
    transform: scale(1.1);
}

.control-btn:active {
    transform: scale(0.95);
}

.instructions {
    background: rgba(255,255,255,0.1);
    padding: 20px;
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

.instructions h3 {
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.instructions ul {
    list-style: none;
    text-align: right;
}

.instructions li {
    margin-bottom: 8px;
    padding: 5px 0;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.instructions li:last-child {
    border-bottom: none;
}

.hidden {
    display: none !important;
}

/* Responsive Design */
@media (max-width: 480px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    #gameCanvas {
        width: 300px;
        height: 300px;
    }
    
    .score-board {
        flex-direction: column;
        gap: 10px;
    }
    
    .overlay-content h2 {
        font-size: 1.5rem;
    }
}
